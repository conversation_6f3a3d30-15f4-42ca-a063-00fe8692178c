import { StatusBar } from "expo-status-bar";
import { StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import "./global.css";
import Test from "./Test";
import Phases from "./components/Phases";

export default function App() {
  return (
    <SafeAreaView style={styles.container} className="bg-primary">
      <View className="component-container">
        <Phases />
      </View>

      <StatusBar style="auto" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: "#FEFFF5",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    width: "100%",
  },
});
