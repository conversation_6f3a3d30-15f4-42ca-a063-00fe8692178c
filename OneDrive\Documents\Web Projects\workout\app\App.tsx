import { StatusBar } from "expo-status-bar";
import { StyleSheet, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import { useEffect } from "react";
import "./global.css";

import Phases from "./components/Phases";
import { useFonts } from "./hooks/useFonts";

export default function App() {
  const fontsLoaded = useFonts();

  if (!fontsLoaded) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container} className="bg-primary">
      <View className="component-container">
        <Phases />
      </View>

      <StatusBar style="auto" />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: "#FEFFF5",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    width: "100%",
  },
});
