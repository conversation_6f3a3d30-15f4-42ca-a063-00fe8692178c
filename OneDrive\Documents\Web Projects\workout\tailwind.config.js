/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all files that contain Nativewind classes.
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        primary: "#212122",
        secondary: "#FEFFF5",
      },
      fontFamily: {
        // Add your custom fonts here
        // Example: 'custom': ['YourFontName', 'sans-serif'],
        // 'heading': ['YourHeadingFont', 'serif'],
        // 'body': ['YourBodyFont', 'sans-serif'],
      },
    },
  },
  plugins: [],
};
