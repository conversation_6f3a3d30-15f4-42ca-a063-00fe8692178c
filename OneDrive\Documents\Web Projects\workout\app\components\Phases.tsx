import { StyleSheet, Text, View } from "react-native";

const Phases = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.date}>Week 2: Day 5</Text>
      <Text style={styles.phase}>Phase: 80%</Text>
      <Text style={styles.repRange}>Rep Range: 6-10</Text>
    </View>
  );
};

export default Phases;

const styles = StyleSheet.create({
  container: {
    display: "flex",
    alignItems: "flex-start",
    justifyContent: "flex-start",
    backgroundColor: "#FEFFF5",
    width: "100%",
  },
  date: {
    fontSize: 20,
    fontWeight: 300,
    fontFamily: "Roboto-Bold",
  },
  phase: {
    marginTop: -16,
    fontSize: 56,
    //fontWeight: 900,
    fontFamily: "Roboto-Thin",
  },
  repRange: {
    marginTop: -8,
    fontSize: 24,
    fontWeight: 500,
  },
});
